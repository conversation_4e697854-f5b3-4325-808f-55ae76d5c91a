services:

  # ------------------------------------------------------------------
  # Cortexa Microservices Platform
  # ------------------------------------------------------------------

  voice-gateway:
    build:
      context: .
      dockerfile: services.voice-gateway/Dockerfile
    container_name: cortexa-voice-gateway
    env_file:
      - services.voice-gateway/.env.compose
    ports:
      - "8002:8002"
    expose:
      - "8002"
    networks:
      - cortexa-network
    labels:
      - "traefik.enable=true"

      # --- Router for the WebSocket endpoint ---
      # This router matches requests for the WebSocket path and applies JWT authentication.
      - "traefik.http.routers.cortexa-ws.rule=Host(`localhost`) && PathPrefix(`/api/v1/ws/call`)"
      - "traefik.http.routers.cortexa-ws.entrypoints=web"
      - "traefik.http.routers.cortexa-ws.middlewares=jwt-auth@file"
      - "traefik.http.routers.cortexa-ws.service=cortexa-service"

      # --- Router for unsecured endpoints (health/metrics) ---
      # This router matches the health and metrics paths without authentication.
      - "traefik.http.routers.cortexa-api.rule=Host(`localhost`) && (PathPrefix(`/api/v1/health`) || PathPrefix(`/metrics`))"
      - "traefik.http.routers.cortexa-api.entrypoints=web"
      - "traefik.http.routers.cortexa-api.service=cortexa-service"

      # --- Service Definition ---
      # Defines how Traefik should connect to this backend service.
      - "traefik.http.services.cortexa-service.loadbalancer.server.port=8002"
    depends_on:
      kafka:
        condition: service_healthy

  # =========== sidecars ===========

  # ------------------------------------------------------------------
  # Traefik Reverse Proxy
  #
  # What: Reverse proxy for all services, provides load balancing, SSL termination, and JWT authentication
  # Why: Centralized point of entry for all services, simplifies service discovery and routing
  # ------------------------------------------------------------------

  traefik:
    image: "traefik:v2.11"
    container_name: cortexa-traefik
    command:
      # Enable the API dashboard (for debugging)
      - "--api.insecure=true"
      # Enable the Docker provider to read service labels
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      # Point to the dynamic configuration file for our middleware
      - "--providers.file.directory=/configuration/"
      - "--providers.file.watch=true"
      # Define the entrypoint for web traffic
      - "--entrypoints.web.address=:80"
      # Enable the experimental plugin feature and specify the JWT middleware
      - "--experimental.plugins.jwt.modulename=github.com/agilezebra/jwt-middleware"
      - "--experimental.plugins.jwt.version=v1.3.2"
    ports:
      - "80:80"      # Public-facing port
      - "8080:8080"  # Traefik dashboard
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./gateway:/configuration/"
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Kafka and Zookeeper
  #
  # What: Message queue for inter-service communication
  # Why: Decouples services, enables asynchronous communication, and provides fault tolerance
  # ------------------------------------------------------------------

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - cortexa-network

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Monitoring and Tracing
  #
  # What: Observability tools for monitoring and tracing
  # Why: Provides insights into service health, performance, and dependencies
  # ------------------------------------------------------------------

  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: cortexa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cortexa-network

  grafana:
    image: grafana/grafana:10.1.0
    container_name: cortexa-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      #- ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      #- ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - cortexa-network

  tempo:
    image: grafana/tempo:2.2.0
    container_name: cortexa-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./monitoring/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # tempo
      - "4317:4317"   # otlp grpc
      - "4318:4318"   # otlp http
    networks:
      - cortexa-network


volumes:
  prometheus_data:
  grafana_data:
  tempo_data:


networks:
  cortexa-network:
    driver: bridge
