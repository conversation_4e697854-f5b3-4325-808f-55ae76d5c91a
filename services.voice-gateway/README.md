# Cortexa Voice Gateway Service

Real-time voice translation service for the Cortexa platform. This service manages stateful, bidirectional WebSocket connections for live speech-to-speech translation.

## Notes

This service assumes to be sitting behind an API Gateway that will proxy the following headers

- `X-User-Id`
- `X-User-Email`
- `X-Token-Audience`
- `X-Token-Issuer`
- `X-Token-Expires-At`
- `X-Token-Issued-At`

TODO: JWT lifetime management is difficult with Web Socket connections as once the connection is upgraded (HTTP/S -> WS) the API Gateway just forwards packets without validating. Therefore we will need to implement token expiry checks manually.