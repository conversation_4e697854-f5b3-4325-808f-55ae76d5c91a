// Configuration template for the Voice Gateway Client
// Copy this file to config.js and update with your actual values

const CONFIG = {
    // Supabase configuration
    // Get these values from your Supabase project dashboard
    supabase: {
        url: 'https://your-project-id.supabase.co',
        anon<PERSON><PERSON>: 'your-supabase-anon-key-here'
    },
    
    // Gateway endpoints
    // Update these if your gateway is running on different URLs/ports
    gateway: {
        baseUrl: 'http://localhost',
        voiceGatewayPath: '/api/v1/voice-gateway',
        websocketUrl: 'ws://localhost:8000',
        websocketPath: '/ws/v1/voice-gateway/call'
    },
    
    // Audio settings
    // These should match your voice gateway service configuration
    audio: {
        sampleRate: 16000,      // Target sample rate for voice processing
        bufferSize: 1024,       // Audio buffer size (power of 2: 256, 512, 1024, 2048, 4096)
        channelCount: 1         // Mono audio
    },
    
    // Default settings
    defaults: {
        targetLanguage: 'es'    // Default target language for translation
    }
};

// Initialize Supabase client
const supabase = window.supabase.createClient(CONFIG.supabase.url, CONFIG.supabase.anonKey);
