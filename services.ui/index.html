<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Gateway WebSocket Client</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>Voice Gateway WebSocket Client</h1>
    
    <!-- Authentication Section -->
    <div class="container">
        <h3>Authentication</h3>
        <div id="authSection">
            <div class="auth-form">
                <div>
                    <label>Email:</label>
                    <input type="email" id="email" placeholder="Enter your email" style="width: 250px;">
                </div>
                <div>
                    <label>Password:</label>
                    <input type="password" id="password" placeholder="Enter your password" style="width: 250px;">
                </div>
                <div>
                    <button id="loginBtn" onclick="login()">Login</button>
                    <button id="signupBtn" onclick="signup()">Sign Up</button>
                    <button id="logoutBtn" onclick="logout()" style="display: none;">Logout</button>
                </div>
            </div>
            <div id="authStatus" class="status disconnected">Not authenticated</div>
        </div>
    </div>
    
    <!-- Connection Settings -->
    <div class="container">
        <h3>Connection Settings</h3>
        <div>
            <label>Gateway URL:</label>
            <input type="text" id="gatewayUrl" value="http://localhost" style="width: 200px;">
        </div>
        <div>
            <label>WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8000" style="width: 200px;">
        </div>
        <div>
            <label>Call ID:</label>
            <input type="text" id="callId" style="width: 200px;">
        </div>
        <div>
            <button id="connectBtn" onclick="connect()" disabled>Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
    </div>

    <!-- Control Messages -->
    <div class="container">
        <h3>Control Messages</h3>
        <div>
            <button onclick="sendPing()" disabled id="pingBtn">Send Ping</button>
            <button onclick="sendConfigUpdate()" disabled id="configBtn">Update Config</button>
            <button onclick="endCall()" disabled id="endCallBtn">End Call</button>
        </div>
        <div>
            <label>Target Language:</label>
            <select id="targetLang">
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
            </select>
        </div>
    </div>

    <!-- Audio Recording -->
    <div class="container">
        <h3>Audio Recording</h3>
        <div class="audio-controls">
            <button onclick="startRecording()" disabled id="recordBtn">Start Recording</button>
            <button onclick="stopRecording()" disabled id="stopBtn">Stop Recording</button>
        </div>
        <div>
            <audio id="audioPlayback" controls style="width: 100%; margin-top: 10px;"></audio>
        </div>
    </div>

    <!-- Translation Results -->
    <div class="container">
        <h3>Translation Results</h3>
        <div id="translationResults" class="translation-results">
            <div class="placeholder">Translation results will appear here...</div>
        </div>
    </div>

    <!-- Message Log -->
    <div class="container">
        <h3>Message Log</h3>
        <div id="messageLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <!-- Include Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include our JavaScript modules -->
    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="websocket.js"></script>
    <script src="audio.js"></script>
    <script src="app.js"></script>
</body>
</html>
