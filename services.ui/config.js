// Configuration for the Voice Gateway Client
const CONFIG = {
    // Supabase configuration
    supabase: {
        url: 'https://jkkxvdaponymlwtmzdrn.supabase.co',
        anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Impra3h2ZGFwb255bWx3dG16ZHJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUxNDE1MTgsImV4cCI6MjA3MDcxNzUxOH0.wS3JIgBDiRREPg7_bL-FCfm2jNoVv2YBEWsrPUt_KfY' // Replace with your actual Supabase anon key
    },
    
    // Gateway endpoints
    gateway: {
        baseUrl: 'http://localhost',
        voiceGatewayPath: '/api/v1/voice-gateway',
        websocketUrl: 'ws://localhost:8000',
        websocketPath: '/ws/v1/voice-gateway/call'
    },
    
    // Audio settings
    audio: {
        sampleRate: 16000,
        bufferSize: 1024,
        channelCount: 1
    },
    
    // Default settings
    defaults: {
        targetLanguage: 'es'
    }
};

// Initialize Supabase client
const supabase = window.supabase.createClient(CONFIG.supabase.url, CONFIG.supabase.anonKey);
