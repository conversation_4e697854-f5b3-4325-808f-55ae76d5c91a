// Audio recording and processing module
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.scriptProcessor = null;
        this.mediaStreamSource = null;
        this.isRecording = false;
    }

    async startRecording() {
        if (!websocketManager || !websocketManager.isConnected()) {
            log('Connect WebSocket before recording.', 'error');
            return;
        }

        if (this.isRecording) {
            log('Already recording', 'error');
            return;
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    channelCount: CONFIG.audio.channelCount,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: false
                }
            });

            // Create AudioContext
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            log(`AudioContext created with sample rate: ${this.audioContext.sampleRate}Hz`, 'info');

            this.mediaStreamSource = this.audioContext.createMediaStreamSource(stream);

            // Use configured buffer size
            this.scriptProcessor = this.audioContext.createScriptProcessor(
                CONFIG.audio.bufferSize, 
                CONFIG.audio.channelCount, 
                CONFIG.audio.channelCount
            );

            this.scriptProcessor.onaudioprocess = (event) => {
                if (!websocketManager || !websocketManager.isConnected()) {
                    return;
                }

                const inputData = event.inputBuffer.getChannelData(0);
                const actualSampleRate = this.audioContext.sampleRate;

                // Resample to target sample rate if needed
                let processedData = inputData;
                if (actualSampleRate !== CONFIG.audio.sampleRate) {
                    processedData = this.resampleAudio(inputData, actualSampleRate, CONFIG.audio.sampleRate);
                }

                // Convert Float32 data to 16-bit PCM
                const buffer = this.convertToInt16PCM(processedData);
                websocketManager.sendAudioData(buffer);
            };

            this.mediaStreamSource.connect(this.scriptProcessor);
            this.scriptProcessor.connect(this.audioContext.destination);

            this.isRecording = true;
            this.updateRecordingUI(true);
            log(`Started real-time audio streaming at ${this.audioContext.sampleRate}Hz...`, 'info');

        } catch (error) {
            log(`Recording error: ${error.message}`, 'error');
            this.stopRecording();
        }
    }

    stopRecording() {
        if (this.scriptProcessor) {
            this.scriptProcessor.disconnect();
            this.scriptProcessor = null;
        }

        if (this.mediaStreamSource) {
            this.mediaStreamSource.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStreamSource.disconnect();
            this.mediaStreamSource = null;
        }

        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }

        this.isRecording = false;
        this.updateRecordingUI(false);
        log('Stopped audio streaming', 'info');
    }

    updateRecordingUI(isRecording) {
        document.getElementById('recordBtn').disabled = isRecording || !websocketManager.isConnected();
        document.getElementById('stopBtn').disabled = !isRecording;
    }

    convertToInt16PCM(float32Array) {
        const buffer = new ArrayBuffer(float32Array.length * 2);
        const view = new DataView(buffer);
        
        for (let i = 0; i < float32Array.length; i++) {
            // Clamp to [-1, 1] range and convert to 16-bit signed integer
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            const intSample = Math.round(sample * 32767);
            view.setInt16(i * 2, intSample, true); // little-endian
        }
        
        return buffer;
    }

    // Simple linear resampling function
    resampleAudio(inputBuffer, inputSampleRate, outputSampleRate) {
        if (inputSampleRate === outputSampleRate) {
            return inputBuffer;
        }

        const ratio = inputSampleRate / outputSampleRate;
        const outputLength = Math.round(inputBuffer.length / ratio);
        const outputBuffer = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            const inputIndex = i * ratio;
            const inputIndexFloor = Math.floor(inputIndex);
            const inputIndexCeil = Math.min(inputIndexFloor + 1, inputBuffer.length - 1);
            const fraction = inputIndex - inputIndexFloor;

            // Linear interpolation
            outputBuffer[i] = inputBuffer[inputIndexFloor] * (1 - fraction) +
                             inputBuffer[inputIndexCeil] * fraction;
        }

        return outputBuffer;
    }
}

// Global audio manager instance
let audioManager;

// Audio functions for HTML onclick handlers
async function startRecording() {
    await audioManager.startRecording();
}

function stopRecording() {
    audioManager.stopRecording();
}
