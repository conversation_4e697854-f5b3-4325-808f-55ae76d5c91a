// Authentication module using Supabase
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.accessToken = null;
        this.init();
    }

    async init() {
        // Check if user is already logged in
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
            this.handleAuthSuccess(session);
        }

        // Listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_IN' && session) {
                this.handleAuthSuccess(session);
            } else if (event === 'SIGNED_OUT') {
                this.handleAuthSignOut();
            }
        });
    }

    handleAuthSuccess(session) {
        this.currentUser = session.user;
        this.accessToken = session.access_token;
        
        log(`Authenticated as: ${this.currentUser.email}`, 'info');
        this.updateAuthUI(true);
        
        // Enable connection button
        document.getElementById('connectBtn').disabled = false;
    }

    handleAuthSignOut() {
        this.currentUser = null;
        this.accessToken = null;
        
        log('Signed out', 'info');
        this.updateAuthUI(false);
        
        // Disable connection and disconnect if connected
        document.getElementById('connectBtn').disabled = true;
        if (websocketManager && websocketManager.isConnected()) {
            websocketManager.disconnect();
        }
    }

    updateAuthUI(isAuthenticated) {
        const authStatus = document.getElementById('authStatus');
        const loginBtn = document.getElementById('loginBtn');
        const signupBtn = document.getElementById('signupBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');

        if (isAuthenticated) {
            authStatus.className = 'status authenticated';
            authStatus.textContent = `Authenticated as: ${this.currentUser.email}`;
            loginBtn.style.display = 'none';
            signupBtn.style.display = 'none';
            logoutBtn.style.display = 'inline-block';
            emailInput.disabled = true;
            passwordInput.disabled = true;
        } else {
            authStatus.className = 'status disconnected';
            authStatus.textContent = 'Not authenticated';
            loginBtn.style.display = 'inline-block';
            signupBtn.style.display = 'inline-block';
            logoutBtn.style.display = 'none';
            emailInput.disabled = false;
            passwordInput.disabled = false;
        }
    }

    async login() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;

        if (!email || !password) {
            log('Please enter both email and password', 'error');
            return;
        }

        try {
            log('Attempting to sign in...', 'info');
            const { data, error } = await supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) {
                throw error;
            }

            log('Login successful!', 'info');
        } catch (error) {
            log(`Login failed: ${error.message}`, 'error');
        }
    }

    async signup() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;

        if (!email || !password) {
            log('Please enter both email and password', 'error');
            return;
        }

        if (password.length < 6) {
            log('Password must be at least 6 characters long', 'error');
            return;
        }

        try {
            log('Attempting to sign up...', 'info');
            const { data, error } = await supabase.auth.signUp({
                email: email,
                password: password
            });

            if (error) {
                throw error;
            }

            if (data.user && !data.session) {
                log('Sign up successful! Please check your email for verification.', 'info');
            } else {
                log('Sign up and login successful!', 'info');
            }
        } catch (error) {
            log(`Sign up failed: ${error.message}`, 'error');
        }
    }

    async logout() {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) {
                throw error;
            }
            log('Logged out successfully', 'info');
        } catch (error) {
            log(`Logout failed: ${error.message}`, 'error');
        }
    }

    getAccessToken() {
        return this.accessToken;
    }

    isAuthenticated() {
        return !!this.accessToken;
    }
}

// Global auth manager instance
let authManager;

// Auth functions for HTML onclick handlers
async function login() {
    await authManager.login();
}

async function signup() {
    await authManager.signup();
}

async function logout() {
    await authManager.logout();
}
