// Main application initialization and utility functions

// Global logging function
function log(message, type = 'info') {
    console.log(message);
    const logDiv = document.getElementById('messageLog');
    const timestamp = new Date().toLocaleTimeString();
    const color = type === 'error' ? '#ff6b6b' : type === 'sent' ? '#4ecdc4' : '#00ff00';
    logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

// Clear log function
function clearLog() {
    document.getElementById('messageLog').innerHTML = '';
}

// Generate random call ID
function generateCallId() {
    return 'call-' + Math.random().toString(36).substr(2, 9);
}

// Initialize application
function initializeApp() {
    // Generate a random call ID
    document.getElementById('callId').value = generateCallId();
    
    // Initialize managers
    authManager = new AuthManager();
    websocketManager = new WebSocketManager();
    audioManager = new AudioManager();
    
    // Set initial UI state
    websocketManager.updateConnectionStatus('disconnected', 'Disconnected');
    
    log('Voice Gateway Client initialized', 'info');
    log('Please authenticate to begin using the service', 'info');
}

// Health check function to test gateway connectivity
async function checkGatewayHealth() {
    const gatewayUrl = document.getElementById('gatewayUrl').value;
    const healthUrl = `${gatewayUrl}${CONFIG.gateway.voiceGatewayPath}/api/v1/health`;
    
    try {
        log(`Checking gateway health: ${healthUrl}`, 'info');
        const response = await fetch(healthUrl);
        
        if (response.ok) {
            const data = await response.json();
            log(`Gateway health check successful: ${JSON.stringify(data)}`, 'info');
            return true;
        } else {
            log(`Gateway health check failed: ${response.status} ${response.statusText}`, 'error');
            return false;
        }
    } catch (error) {
        log(`Gateway health check error: ${error.message}`, 'error');
        return false;
    }
}

// Test gateway endpoints
async function testGatewayEndpoints() {
    if (!authManager.isAuthenticated()) {
        log('Please authenticate first', 'error');
        return;
    }

    const gatewayUrl = document.getElementById('gatewayUrl').value;
    const token = authManager.getAccessToken();
    
    // Test health endpoint
    await checkGatewayHealth();
    
    // Test metrics endpoint
    try {
        const metricsUrl = `${gatewayUrl}${CONFIG.gateway.voiceGatewayPath}/metrics`;
        log(`Testing metrics endpoint: ${metricsUrl}`, 'info');
        
        const response = await fetch(metricsUrl, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            log('Metrics endpoint accessible', 'info');
        } else {
            log(`Metrics endpoint failed: ${response.status}`, 'error');
        }
    } catch (error) {
        log(`Metrics endpoint error: ${error.message}`, 'error');
    }
}

// Handle keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Ctrl+Enter to connect/disconnect
    if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault();
        if (websocketManager.isConnected()) {
            disconnect();
        } else if (authManager.isAuthenticated()) {
            connect();
        }
    }
    
    // Space to start/stop recording (when connected)
    if (event.code === 'Space' && websocketManager.isConnected()) {
        event.preventDefault();
        if (audioManager.isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    }
});

// Handle Enter key in auth fields
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    
    function handleEnterKey(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            login();
        }
    }
    
    emailInput.addEventListener('keydown', handleEnterKey);
    passwordInput.addEventListener('keydown', handleEnterKey);
});

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    
    // Add test button for gateway endpoints (for debugging)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const testButton = document.createElement('button');
        testButton.textContent = 'Test Gateway Endpoints';
        testButton.onclick = testGatewayEndpoints;
        testButton.className = 'secondary';
        
        const connectionContainer = document.querySelector('.container:nth-child(3)');
        connectionContainer.appendChild(testButton);
    }
});

// Export functions for debugging in console
window.debugFunctions = {
    checkGatewayHealth,
    testGatewayEndpoints,
    generateCallId,
    log
};
