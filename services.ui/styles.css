body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.container {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status {
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-weight: bold;
}

.status.connected { 
    background: #d4edda; 
    color: #155724; 
    border: 1px solid #c3e6cb;
}

.status.disconnected { 
    background: #f8d7da; 
    color: #721c24; 
    border: 1px solid #f5c6cb;
}

.status.error { 
    background: #fff3cd; 
    color: #856404; 
    border: 1px solid #ffeaa7;
}

.status.authenticated {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

button {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin: 5px;
    font-size: 14px;
    transition: background-color 0.2s;
}

button:hover { 
    background: #0056b3; 
}

button:disabled { 
    background: #6c757d; 
    cursor: not-allowed; 
}

button.secondary {
    background: #6c757d;
}

button.secondary:hover {
    background: #545b62;
}

button.danger {
    background: #dc3545;
}

button.danger:hover {
    background: #c82333;
}

input, select {
    padding: 8px;
    margin: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

input:focus, select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.log {
    background: #000;
    color: #00ff00;
    padding: 10px;
    height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.audio-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.translation-results {
    background: #f8f9fa; 
    padding: 15px; 
    border-radius: 4px; 
    min-height: 200px; 
    max-height: 300px; 
    overflow-y: auto; 
    font-family: Arial, sans-serif;
    border: 1px solid #dee2e6;
}

.translation-results .placeholder {
    color: #6c757d; 
    font-style: italic;
}

.translation-result {
    margin-bottom: 15px; 
    padding: 10px; 
    border-left: 3px solid #007bff; 
    background: white; 
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.translation-result .timestamp {
    font-size: 12px; 
    color: #6c757d; 
    margin-bottom: 5px;
}

.translation-result .original {
    margin-bottom: 8px;
}

.translation-result .translated {
    color: #007bff;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.auth-form > div {
    display: flex;
    align-items: center;
    gap: 10px;
}

.auth-form label {
    min-width: 80px;
    font-weight: bold;
}

h1 {
    color: #343a40;
    text-align: center;
    margin-bottom: 30px;
}

h3 {
    color: #495057;
    margin-top: 0;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

@media (max-width: 600px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 15px;
    }
    
    .audio-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .auth-form > div {
        flex-direction: column;
        align-items: stretch;
    }
    
    .auth-form label {
        min-width: auto;
    }
}
